"""
SQLAlchemy model for Pinnacle PatientRepresentation data.

This module provides the PatientRepresentation model for representing patient position and setup information.
"""

from typing import TYPE_CHECKING, Optional

from sqlalchemy import Column, String, Integer, Float, ForeignKey
from sqlalchemy.orm import relationship, Mapped

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.trial import Trial


class PatientRepresentation(PinnacleBase):
    """
    Model representing patient representation and imaging parameters for treatment planning.

    This class stores comprehensive patient representation information that defines how
    patient anatomy is modeled and processed for dose calculation in a treatment trial.
    It encompasses CT-to-density conversion tables, dose calculation parameters, and
    patient volume definitions that are essential for accurate treatment planning.

    The PatientRepresentation model serves as the bridge between imaging data and dose
    calculation engines, ensuring that patient anatomy is properly represented with
    appropriate material properties and calculation parameters for the specific trial.

    Attributes:
        id (int): Primary key inherited from PinnacleBase
        patient_volume_name (str): Name of the patient volume used for calculations
        ct_to_density_name (str): Name of the CT-to-density conversion table
        ct_to_density_version (str): Version of the CT-to-density table
        dm_table_name (str): Name of the dose matrix table
        dm_table_version (str): Version of the dose matrix table
        top_z_padding (int): Z-axis padding above the patient volume in voxels
        bottom_z_padding (int): Z-axis padding below the patient volume in voxels
        high_res_z_spacing_for_variable (float): High-resolution Z spacing for variable calculations
        outside_patient_is_ct_number (int): Flag indicating if outside patient uses CT numbers
        outside_patient_air_threshold (float): Air threshold for outside patient regions
        ct_to_density_table_accepted (int): Flag indicating CT-to-density table acceptance
        ct_to_density_table_extended (int): Flag indicating CT-to-density table extension
        ct_to_stopping_power_table_name (str): Name of CT-to-stopping-power table
        ct_to_stopping_power_version (str): Version of CT-to-stopping-power table
        ct_to_stopping_power_extended (int): Flag for stopping power table extension
        ct_to_stopping_power_accepted (int): Flag for stopping power table acceptance
        trial_id (int): Foreign key to the parent Trial

    Relationships:
        trial (Trial): Parent trial that owns this patient representation (many-to-one)

    Database Mapping:
        This model maps to the "PatientRepresentation" table in the Pinnacle database schema.
        Column names follow Pinnacle's PascalCase convention for database compatibility.

    Usage:
        PatientRepresentation instances are typically created automatically when loading
        Trial data from Pinnacle files. They define the patient modeling parameters
        used for dose calculation within a specific trial.

        Example:
            >>> patient_rep = PatientRepresentation(
            ...     patient_volume_name="BODY",
            ...     ct_to_density_name="Standard CT",
            ...     top_z_padding=10,
            ...     bottom_z_padding=15
            ... )

    Clinical Significance:
        The patient representation parameters directly affect dose calculation accuracy:
        - CT-to-density tables convert Hounsfield units to physical densities
        - Padding parameters ensure adequate calculation margins
        - Stopping power tables are essential for proton therapy calculations
        - Air thresholds define tissue boundaries for dose calculation
    """

    __tablename__ = "PatientRepresentation"

    # Primary key is inherited from PinnacleBase
    patient_volume_name: Mapped[Optional[str]] = Column("PatientVolumeName", String, nullable=True)
    ct_to_density_name: Mapped[Optional[str]] = Column("CTToDensityName", String, nullable=True)
    ct_to_density_version: Mapped[Optional[str]] = Column(
        "CTToDensityVersion", String, nullable=True
    )
    dm_table_name: Mapped[Optional[str]] = Column("DMTableName", String, nullable=True)
    dm_table_version: Mapped[Optional[str]] = Column("DMTableVersion", String, nullable=True)
    top_z_padding: Mapped[Optional[int]] = Column("TopZPadding", Integer, nullable=True)
    bottom_z_padding: Mapped[Optional[int]] = Column("BottomZPadding", Integer, nullable=True)
    high_res_z_spacing_for_variable: Mapped[Optional[float]] = Column(
        "HighResZSpacingForVariable", Float, nullable=True
    )
    outside_patient_is_ct_number: Mapped[Optional[int]] = Column(
        "OutsidePatientIsCTNumber", Integer, nullable=True
    )
    outside_patient_air_threshold: Mapped[Optional[float]] = Column(
        "OutsidePatientAirThreshold", Float, nullable=True
    )
    ct_to_density_table_accepted: Mapped[Optional[int]] = Column(
        "CTToDensityTableAccepted", Integer, nullable=True
    )
    ct_to_density_table_extended: Mapped[Optional[int]] = Column(
        "CTToDensityTableExtended", Integer, nullable=True
    )
    ct_to_stopping_power_table_name: Mapped[Optional[str]] = Column(
        "CTToStoppingPowerTableName", String, nullable=True
    )
    ct_to_stopping_power_version: Mapped[Optional[str]] = Column(
        "CTToStoppingPowerVersion", String, nullable=True
    )
    ct_to_stopping_power_extended: Mapped[Optional[int]] = Column(
        "CTToStoppingPowerExtended", Integer, nullable=True
    )
    ct_to_stopping_power_accepted: Mapped[Optional[int]] = Column(
        "CTToStoppingPowerAccepted", Integer, nullable=True
    )

    # Parent relationship
    trial_id: Mapped[Optional[int]] = Column(
        "TrialID", Integer, ForeignKey("Trial.ID"), nullable=True
    )
    trial: Mapped[Optional["Trial"]] = relationship(
        "Trial",
        back_populates="patient_representation",
        lazy="select"
    )

    def __init__(self, **kwargs):
        """
        Initialize a PatientRepresentation instance.

        Args:
            **kwargs: Keyword arguments used to initialize PatientRepresentation attributes.
                Common attributes include:
                - patient_volume_name: Name of the patient volume
                - ct_to_density_name: CT-to-density conversion table name
                - top_z_padding: Z-axis padding above patient volume
                - bottom_z_padding: Z-axis padding below patient volume
                - trial: Parent Trial object

        Relationships:
            trial (Trial): The parent Trial to which this PatientRepresentation belongs (many-to-one).

        Example:
            >>> patient_rep = PatientRepresentation(
            ...     patient_volume_name="BODY",
            ...     ct_to_density_name="Standard CT",
            ...     top_z_padding=10,
            ...     bottom_z_padding=15
            ... )
        """
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """
        Return a string representation of this patient representation.

        Returns:
            str: A string containing the patient representation's ID, volume name, and padding info.
        """
        return f"<PatientRepresentation(id={self.id}, patient_volume_name='{self.patient_volume_name}', top_z_padding={self.top_z_padding}, bottom_z_padding={self.bottom_z_padding})>"
