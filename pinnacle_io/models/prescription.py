"""
SQLAlchemy model for Pinnacle Prescription data.

This module provides the Prescription model for representing treatment prescription
details within a Pinnacle trial. It includes dose targets, fractionation schemes,
and normalization parameters.
"""

from __future__ import annotations
from typing import Optional, TYPE_CHECKING

from sqlalchemy import Column, String, Integer, Float, ForeignKey
from sqlalchemy.orm import relationship, Mapped

from pinnacle_io.models.pinnacle_base import PinnacleBase

if TYPE_CHECKING:
    from pinnacle_io.models.trial import Trial


class Prescription(PinnacleBase):
    """
    Represents a single prescription within a treatment trial.

    In Pinnacle, a trial can have multiple prescriptions, each defining a specific
    treatment intent, such as the dose to be delivered to a target volume over a
    certain number of fractions. This model captures all the parameters that
    define a prescription.

    Attributes:
        id (int): The unique identifier for the prescription.
        name (str): The name of the prescription (e.g., "PTV Boost").
        prescription_dose (float): The total dose prescribed in Gray (Gy).
        number_of_fractions (int): The number of fractions over which the dose
            is to be delivered.
        prescription_percent (int): The isodose line (as a percentage) to which
            the prescription_dose is normalized.
        prescription_point (str): The name of the Point of Interest (POI) used
            for the prescription.
        normalization_method (str): The method used for dose normalization
            (e.g., "MaxDose", "ToPoint").

    Relationships:
        trial (Trial): The parent Trial to which this Prescription belongs. This is
            a many-to-one relationship, as a trial can have multiple
            prescriptions. The relationship is eagerly loaded using a 'joined'
            strategy for performance, as prescriptions are almost always
            accessed with their parent trial.
    """

    __tablename__ = "Prescription"

    # Primary key is inherited from PinnacleBase
    name: Mapped[Optional[str]] = Column("Name", String, nullable=True)
    requested_monitor_units_per_fraction: Mapped[Optional[int]] = Column(
        "RequestedMonitorUnitsPerFraction", Integer, nullable=True
    )
    prescription_dose: Mapped[Optional[float]] = Column("PrescriptionDose", Float, nullable=True)
    prescription_percent: Mapped[Optional[int]] = Column(
        "PrescriptionPercent", Integer, nullable=True
    )
    number_of_fractions: Mapped[Optional[int]] = Column("NumberOfFractions", Integer, nullable=True)
    prescription_point: Mapped[Optional[str]] = Column("PrescriptionPoint", String, nullable=True)
    method: Mapped[Optional[str]] = Column("Method", String, nullable=True)
    normalization_method: Mapped[Optional[str]] = Column(
        "NormalizationMethod", String, nullable=True
    )
    prescription_period: Mapped[Optional[str]] = Column("PrescriptionPeriod", String, nullable=True)
    weights_proportional_to: Mapped[Optional[str]] = Column(
        "WeightsProportionalTo", String, nullable=True
    )
    dose_uncertainty: Mapped[Optional[int]] = Column("DoseUncertainty", Integer, nullable=True)
    prescription_uncertainty: Mapped[Optional[int]] = Column(
        "PrescriptionUncertainty", Integer, nullable=True
    )
    dose_uncertainty_valid: Mapped[Optional[int]] = Column(
        "DoseUncertaintyValid", Integer, nullable=True
    )
    prescrip_uncertainty_valid: Mapped[Optional[int]] = Column(
        "PrescripUncertaintyValid", Integer, nullable=True
    )
    color: Mapped[Optional[str]] = Column("Color", String, nullable=True)

    # Foreign key to the parent Trial
    trial_id: Mapped[int] = Column("TrialID", Integer, ForeignKey("Trial.ID"), nullable=False)

    # Parent relationship, eagerly loaded for performance.
    trial: Mapped["Trial"] = relationship(
        "Trial",
        back_populates="prescription_list",
        lazy="joined"
    )

    def __repr__(self) -> str:
        """
        Provide a developer-friendly string representation of the Prescription.
        """
        return (
            f"<Prescription(id={self.id}, name='{self.name}', "
            f"dose={self.prescription_dose}, fx={self.number_of_fractions})>"
        )
